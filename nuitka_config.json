{"app_name": "LinTrans", "version": "0.2.1", "description": "Lin-Trans 语音转录和翻译工具", "entry_point": "run.py", "output_dir": "dist", "icon": null, "console_mode": "disable", "optimization_level": 2, "include_packages": ["nice_ui", "components", "agent", "app", "services", "utils", "orm", "config", "tools", "videotrans", "vendor"], "include_data_dirs": ["components/assets", "components/themes", "config", "nice_ui/language", "nice_ui/docs"], "include_data_files": ["components/lin_resource_rc.py", "config/api_config.yaml", "orm/linlin.db"], "exclude_packages": ["pytest", "unittest", "doctest", "tkinter", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jup<PERSON><PERSON>", "IPython", "notebook", "sphinx", "setuptools", "pip", "wheel", "distutils", "test", "tests", "_pytest"], "nofollow_imports": ["torch", "<PERSON><PERSON><PERSON>", "scipy", "numpy", "modelscope", "funasr", "transformers", "datasets", "huggingface_hub", "tensorboard", "wandb", "mlflow"], "pyside6_plugins": ["platforms", "imageformats", "iconengines", "styles", "multimedia", "networkinformation"], "qt_translations": ["zh_CN", "en_US"], "build_profiles": {"minimal": {"description": "最小化构建，仅包含核心功能", "exclude_packages": ["torch", "<PERSON><PERSON><PERSON>", "scipy", "numpy", "modelscope", "funasr"]}, "standard": {"description": "标准构建，包含大部分功能", "include_packages": ["nice_ui", "components", "agent", "app", "services", "utils"]}, "full": {"description": "完整构建，包含所有功能", "include_packages": "all"}}, "optimization": {"enable_lto": true, "enable_anti_bloat": true, "python_flags": ["no_site", "no_warnings", "no_docstrings"]}, "windows_specific": {"company_name": "RiftCover", "product_name": "Lin-Trans", "file_description": "语音转录和翻译工具", "copyright": "Copyright (C) 2025 RiftCover", "version_info": "*******", "uac_admin": false, "disable_console": true}, "macos_specific": {"bundle_identifier": "com.riftcover.lintrans", "bundle_name": "Lin-Trans", "bundle_version": "0.2.1", "info_plist": {"CFBundleDisplayName": "Lin-Trans", "CFBundleShortVersionString": "0.2.1", "NSHighResolutionCapable": true, "NSRequiresAquaSystemAppearance": false}}, "linux_specific": {"desktop_file": {"name": "Lin-Trans", "comment": "语音转录和翻译工具", "categories": ["AudioVideo", "Audio", "Utility"], "mime_types": ["audio/wav", "audio/mp3", "video/mp4"]}}, "advanced_options": {"follow_stdlib": false, "follow_imports": true, "include_qt_translations": true, "include_qt_plugins": true, "onefile": false, "standalone": true, "remove_output": true, "assume_yes_for_downloads": true, "show_progress": true, "show_memory": true, "jobs": 4}, "debug_options": {"debug_mode": false, "trace_execution": false, "keep_debug_info": false, "generate_pyi": false}, "post_build": {"create_installer": false, "compress_executable": false, "sign_executable": false, "run_tests": false, "cleanup_build": true}, "environment": {"required_python_version": ">=3.11,<3.13", "required_nuitka_version": ">=2.4.8", "target_architectures": ["x86_64"], "supported_platforms": ["windows", "linux", "darwin"]}}