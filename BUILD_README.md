# Lin-Trans Nuitka 构建说明

本项目使用 Nuitka 进行打包，将 Python 应用程序编译为独立的可执行文件。

## 构建要求

### 系统要求
- Windows 10/11 (推荐)
- Python 3.11 或 3.12
- Visual Studio Build Tools 或 Visual Studio Community

### 必要依赖
```bash
pip install nuitka>=2.4.8
```

### 项目依赖
确保已安装项目的所有依赖：
```bash
pip install -r requirements.txt
```

## 构建方法

### 方法1: 使用批处理脚本（Windows 推荐）
```bash
# 双击运行或在命令行执行
build.bat
```
提供交互式菜单，支持：
- 发布版本构建
- 调试版本构建  
- 清理后构建
- 仅清理构建文件

### 方法2: 使用主构建脚本
```bash
# 构建发布版本
python build_nuitka_main.py

# 构建调试版本（显示控制台）
python build_nuitka_main.py --debug

# 清理后构建
python build_nuitka_main.py --clean

# 使用自定义配置
python build_nuitka_main.py --config custom_config.json
```

### 方法3: 快速构建（测试用）
```bash
# 快速构建发布版本
python quick_build.py

# 快速构建调试版本
python quick_build.py --debug

# 清理后构建
python quick_build.py --clean
```

## 配置文件

### nuitka_config.json
主配置文件，包含详细的构建选项：

- **app_name**: 应用程序名称
- **entry_point**: 入口文件（默认 run.py）
- **output_dir**: 输出目录（默认 dist）
- **include_packages**: 要包含的 Python 包
- **include_data_dirs**: 要包含的数据目录
- **exclude_packages**: 要排除的包
- **pyside6_plugins**: PySide6 插件配置

### 构建配置文件
可以根据需要修改 `nuitka_config.json` 中的配置：

```json
{
  "app_name": "LinTrans",
  "entry_point": "run.py",
  "console_mode": "disable",
  "optimization_level": 2,
  "include_packages": [
    "nice_ui",
    "components",
    "agent"
  ]
}
```

## 构建输出

### 文件结构
```
dist/
├── LinTrans.exe          # 主可执行文件
├── _internal/            # 依赖文件目录
│   ├── PySide6/         # PySide6 库文件
│   ├── components/      # 项目组件
│   ├── nice_ui/         # UI 模块
│   └── ...              # 其他依赖
└── ...
```

### 发布版本 vs 调试版本

**发布版本** (`LinTrans.exe`):
- 不显示控制台窗口
- 优化编译，体积较小
- 适合最终用户使用

**调试版本** (`LinTrans_Debug.exe`):
- 显示控制台输出
- 包含调试信息
- 便于开发调试

## 常见问题

### 1. 构建失败
- 检查 Python 版本是否为 3.11 或 3.12
- 确保已安装所有依赖：`pip install -r requirements.txt`
- 检查 Visual Studio Build Tools 是否正确安装

### 2. 可执行文件过大
- 使用 `--nofollow-import-to` 排除不需要的大型包
- 考虑使用最小化构建配置
- 检查是否包含了不必要的数据文件

### 3. 运行时错误
- 使用调试版本查看详细错误信息
- 检查是否缺少必要的数据文件或配置
- 确保目标机器安装了 Visual C++ Redistributable

### 4. PySide6 相关问题
- 确保包含了必要的 Qt 插件
- 检查 Qt 翻译文件是否正确包含
- 验证 PySide6 版本兼容性

## 性能优化

### 编译优化
- 使用 `--optimization=2` 启用最高优化级别
- 启用 `--enable-plugin=anti-bloat` 减少体积
- 使用 `--python-flag=no_site` 跳过 site 模块

### 包含策略
- 仅包含必要的包和模块
- 使用 `--nofollow-import-to` 排除大型依赖
- 动态加载可选功能模块

## 部署说明

### Windows 部署
1. 将整个 `dist` 目录复制到目标机器
2. 确保目标机器安装了 Visual C++ Redistributable
3. 运行 `LinTrans.exe` 启动应用程序

### 系统要求
- Windows 10 或更高版本
- Visual C++ Redistributable 2015-2022
- 至少 4GB 可用内存
- 至少 2GB 可用磁盘空间

## 高级配置

### 自定义构建配置
可以创建自定义配置文件来满足特定需求：

```json
{
  "build_profiles": {
    "minimal": {
      "description": "最小化构建",
      "exclude_packages": ["torch", "scipy", "numpy"]
    },
    "full": {
      "description": "完整构建", 
      "include_packages": "all"
    }
  }
}
```

### 环境变量
- `NUITKA_CACHE_DIR`: Nuitka 缓存目录
- `PYTHONPATH`: Python 模块搜索路径
- `QT_PLUGIN_PATH`: Qt 插件路径

## 技术支持

如果遇到构建问题，请：
1. 查看构建日志中的错误信息
2. 检查依赖版本兼容性
3. 参考 Nuitka 官方文档
4. 在项目 Issues 中报告问题
