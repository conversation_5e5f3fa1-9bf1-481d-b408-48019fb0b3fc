@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo    Lin-Trans Nuitka 构建工具
echo ========================================
echo.

:: 检查 Python 环境
echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未找到，请确保 Python 已安装并添加到 PATH
    pause
    exit /b 1
)

:: 检查虚拟环境
if defined VIRTUAL_ENV (
    echo ✅ 检测到虚拟环境: %VIRTUAL_ENV%
) else (
    echo ⚠️  未检测到虚拟环境，建议在虚拟环境中构建
    echo.
    set /p continue="是否继续? (y/N): "
    if /i not "!continue!"=="y" (
        echo 构建已取消
        pause
        exit /b 0
    )
)

:: 检查 Nuitka
echo 🔍 检查 Nuitka...
python -m nuitka --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Nuitka 未安装，正在安装...
    pip install nuitka>=2.4.8
    if errorlevel 1 (
        echo ❌ Nuitka 安装失败
        pause
        exit /b 1
    )
)

:: 显示菜单
echo.
echo 请选择构建模式:
echo 1. 发布版本 (无控制台)
echo 2. 调试版本 (显示控制台)
echo 3. 清理后构建发布版本
echo 4. 清理后构建调试版本
echo 5. 仅清理构建文件
echo 0. 退出
echo.

set /p choice="请输入选择 (0-5): "

if "%choice%"=="0" (
    echo 退出构建
    pause
    exit /b 0
)

if "%choice%"=="1" (
    echo 🚀 开始构建发布版本...
    python build_nuitka_main.py
    goto :end
)

if "%choice%"=="2" (
    echo 🚀 开始构建调试版本...
    python build_nuitka_main.py --debug
    goto :end
)

if "%choice%"=="3" (
    echo 🚀 清理后构建发布版本...
    python build_nuitka_main.py --clean
    goto :end
)

if "%choice%"=="4" (
    echo 🚀 清理后构建调试版本...
    python build_nuitka_main.py --clean --debug
    goto :end
)

if "%choice%"=="5" (
    echo 🧹 清理构建文件...
    if exist dist rmdir /s /q dist
    if exist build rmdir /s /q build
    if exist __pycache__ rmdir /s /q __pycache__
    for /d %%i in (*.build) do rmdir /s /q "%%i"
    for /d %%i in (*.dist) do rmdir /s /q "%%i"
    for /d %%i in (*.onefile-build) do rmdir /s /q "%%i"
    echo ✅ 清理完成
    goto :end
)

echo ❌ 无效选择
pause
exit /b 1

:end
if errorlevel 1 (
    echo.
    echo ❌ 构建失败，请检查错误信息
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 操作完成
    pause
)
