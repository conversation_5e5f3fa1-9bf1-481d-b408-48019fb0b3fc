#!/usr/bin/env python3
"""
构建结果测试脚本
用于验证 Nuitka 构建的可执行文件是否正常工作
"""

import os
import sys
import subprocess
import time
from pathlib import Path


def find_executable():
    """查找构建的可执行文件"""
    dist_dir = Path("dist")
    
    if not dist_dir.exists():
        return None
    
    # 查找可执行文件
    exe_files = []
    
    # Windows
    for exe_file in dist_dir.glob("*.exe"):
        exe_files.append(exe_file)
    
    # Linux/macOS
    for file in dist_dir.iterdir():
        if file.is_file() and os.access(file, os.X_OK) and not file.suffix:
            exe_files.append(file)
    
    if not exe_files:
        return None
    
    # 优先返回非调试版本
    for exe_file in exe_files:
        if "debug" not in exe_file.name.lower():
            return exe_file
    
    return exe_files[0]


def test_executable(exe_path, timeout=30):
    """测试可执行文件"""
    print(f"🧪 测试可执行文件: {exe_path}")
    
    # 检查文件存在
    if not exe_path.exists():
        print(f"❌ 文件不存在: {exe_path}")
        return False
    
    # 检查文件大小
    size_mb = exe_path.stat().st_size / (1024 * 1024)
    print(f"📏 文件大小: {size_mb:.1f} MB")
    
    # 检查是否可执行
    if not os.access(exe_path, os.X_OK):
        print(f"❌ 文件不可执行: {exe_path}")
        return False
    
    print("✅ 基本检查通过")
    
    # 尝试运行（短时间）
    print(f"🚀 尝试运行程序（{timeout}秒超时）...")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            [str(exe_path)],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待一段时间
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print("✅ 程序成功启动")
            
            # 终止进程
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
                process.wait()
            
            print("✅ 程序正常终止")
            return True
        else:
            # 进程已退出，检查返回码
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                print("✅ 程序正常退出")
                return True
            else:
                print(f"❌ 程序异常退出，返回码: {process.returncode}")
                if stderr:
                    print(f"错误输出: {stderr}")
                return False
    
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False


def check_dependencies(exe_path):
    """检查依赖文件"""
    print("🔍 检查依赖文件...")
    
    dist_dir = exe_path.parent
    internal_dir = dist_dir / "_internal"
    
    if not internal_dir.exists():
        print("❌ _internal 目录不存在")
        return False
    
    # 检查关键依赖
    critical_deps = [
        "PySide6",
        "httpx", 
        "loguru"
    ]
    
    missing_deps = []
    for dep in critical_deps:
        dep_found = False
        
        # 在 _internal 目录中查找
        for item in internal_dir.rglob(f"*{dep}*"):
            if item.is_dir() or item.is_file():
                dep_found = True
                break
        
        if dep_found:
            print(f"   ✅ {dep}")
        else:
            print(f"   ❌ {dep}")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"⚠️  缺少依赖: {', '.join(missing_deps)}")
        return False
    
    print("✅ 依赖检查通过")
    return True


def check_data_files(exe_path):
    """检查数据文件"""
    print("🔍 检查数据文件...")
    
    dist_dir = exe_path.parent
    
    # 检查关键数据目录
    data_dirs = [
        "components",
        "config"
    ]
    
    missing_dirs = []
    for data_dir in data_dirs:
        dir_path = dist_dir / data_dir
        if dir_path.exists():
            print(f"   ✅ {data_dir}/")
        else:
            # 在 _internal 中查找
            internal_path = dist_dir / "_internal" / data_dir
            if internal_path.exists():
                print(f"   ✅ {data_dir}/ (在 _internal 中)")
            else:
                print(f"   ❌ {data_dir}/")
                missing_dirs.append(data_dir)
    
    if missing_dirs:
        print(f"⚠️  缺少数据目录: {', '.join(missing_dirs)}")
        return False
    
    print("✅ 数据文件检查通过")
    return True


def main():
    """主函数"""
    print("🧪 Lin-Trans 构建测试工具")
    print("=" * 40)
    
    # 查找可执行文件
    exe_path = find_executable()
    
    if not exe_path:
        print("❌ 未找到构建的可执行文件")
        print("请先运行构建脚本生成可执行文件")
        sys.exit(1)
    
    print(f"📦 找到可执行文件: {exe_path}")
    print()
    
    # 执行测试
    tests = [
        ("可执行文件测试", lambda: test_executable(exe_path)),
        ("依赖文件检查", lambda: check_dependencies(exe_path)),
        ("数据文件检查", lambda: check_data_files(exe_path))
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔬 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"💥 {test_name} 异常: {e}")
        
        print()
    
    # 总结
    print("📊 测试总结")
    print("=" * 40)
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！构建成功！")
        print(f"\n📋 使用说明:")
        print(f"1. 运行 {exe_path} 启动程序")
        print(f"2. 将 {exe_path.parent} 目录复制到目标机器")
        print(f"3. 确保目标机器安装了 Visual C++ Redistributable")
        return True
    else:
        print("⚠️  部分测试失败，请检查构建配置")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
