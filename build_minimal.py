#!/usr/bin/env python3
"""
最小化构建脚本，专门用于测试 httpx 问题
"""

import sys
import subprocess
import time  # 1. 导入 time 模块
from pathlib import Path


def build_minimal():
    """构建 minimal_test.py"""

    # 检查是否存在之前的构建文件
    build_dir = Path("dist/minimal_test.build")
    dist_dir = Path("dist/minimal_test.dist")
    is_first_build = not (build_dir.exists() and dist_dir.exists())

    if is_first_build:
        print("🔄 首次构建，将创建缓存...")
    else:
        print("🚀 检测到之前的构建文件，Nuitka 将使用增量编译...")

    cmd = [
        sys.executable, "-m", "nuitka",

        "--main=minimal_test.py",
        "--output-filename=minimal_test.exe",
        "--output-dir=dist",

        "--standalone",
        "--assume-yes-for-downloads",

        "--follow-imports",

        "--windows-console-mode=attach",
        "--show-progress",
        "--show-memory",  # 显示内存使用情况，有助于调试缓存效果

        # 优化选项，有助于缓存效果
        "--enable-plugin=anti-bloat",  # 减少不必要的模块，提高缓存效率
    ]

    print("🚀 开始构建 minimal_test.py...")

    # 2. 在执行命令前记录开始时间
    start_time = time.monotonic()

    # 执行构建命令
    subprocess.run(cmd, check=True)

    # 3. 在执行命令后记录结束时间
    end_time = time.monotonic()

    # 4. 计算并打印耗时
    duration = end_time - start_time
    print(f"🎉 构建完成: dist/minimal_test.exe")
    print(f"⏱️ 本次构建耗时: {duration:.2f} 秒")  # 使用 f-string 格式化输出为两位小数


def clean_cache():
    """清理 Nuitka 构建缓存"""
    import shutil

    # Nuitka 的构建文件
    build_dir = Path("dist/minimal_test.build")
    dist_dir = Path("dist/minimal_test.dist")
    exe_file = Path("dist/minimal_test.exe")

    files_to_clean = [
        (build_dir, "构建目录"),
        (dist_dir, "分发目录"),
        (exe_file, "可执行文件")
    ]

    cleaned_any = False
    for path, description in files_to_clean:
        if path.exists():
            print(f"🧹 清理 {description}: {path}")
            if path.is_dir():
                shutil.rmtree(path, ignore_errors=True)
            else:
                path.unlink(missing_ok=True)
            cleaned_any = True

    if cleaned_any:
        print("✅ 构建缓存已清理，下次构建将重新开始")
    else:
        print("ℹ️ 没有找到需要清理的构建文件")


def main():
    import argparse

    parser = argparse.ArgumentParser(description="最小化构建脚本")
    parser.add_argument("--clean-cache", action="store_true", help="清理缓存后重新构建")
    args = parser.parse_args()

    if not Path("minimal_test.py").exists():
        print("❌ 错误: 找不到 minimal_test.py")
        sys.exit(1)

    print("🧪 构建流程：主程序")

    # 如果指定了清理缓存，先清理
    if args.clean_cache:
        clean_cache()

    build_minimal()


if __name__ == "__main__":
    main()