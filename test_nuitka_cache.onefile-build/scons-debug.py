# -*- coding: utf-8 -*-

import sys
import os
import subprocess

exit_code = subprocess.call(
    ['D:\\dcode\\lin_trans\\.venv\\Scripts\\python.exe', '-W', 'ignore', 'D:\\dcode\\lin_trans\\.venv\\Lib\\site-packages\\nuitka\\build\\inline_copy\\bin\\scons.py', '--quiet', '-f', 'D:\\dcode\\lin_trans\\.venv\\Lib\\site-packages\\nuitka\\build\\Onefile.scons', '--jobs', '6', '--warn=no-deprecated', '--no-site-dir', 'nuitka_src=D:\\dcode\\lin_trans\\.venv\\Lib\\site-packages\\nuitka\\build', 'python_version=3.11', 'python_prefix=C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311', 'experimental=', 'debug_modes=', 'deployment=false', 'no_deployment=', 'console_mode=force', 'noelf_mode=true', 'cpp_defines=_NUITKA_PLUGIN_MULTIPROCESSING_ENABLED=1', 'target_arch=x86_64', 'module_mode=false', 'dll_mode=true', 'exe_mode=false', 'standalone_mode=true', 'onefile_mode=true', 'onefile_temp_mode=true', 'result_exe=D:\\dcode\\lin_trans\\test_nuitka_cache.exe', 'source_dir=.', 'debug_mode=false', 'trace_mode=false', 'onefile_splash_screen=false'],
    env={'CONDA_EXE': 'C:\\ac\\Scripts\\conda.exe','PSEXECUTIONPOLICYPREFERENCE': 'Bypass','PYTHONHASHSEED': '0','POSH_INSTALLER': 'ws','COMMONPROGRAMW6432': 'C:\\Program Files\\Common Files','PROGRAMW6432': 'C:\\Program Files','DATAGRIP_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\datagrip.vmoptions','JETBRAINS_CLIENT_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\jetbrains_client.vmoptions','CLION_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\clion.vmoptions','PYCHARM': 'C:\\Program Files\\JetBrains\\PyCharm 2023.1.1\\bin;','USERNAME': 'gaosh','ALLUSERSPROFILE': 'C:\\ProgramData','USERPROFILE': 'C:\\Users\\<USER>\\pojie\\ja-netfilter-all\\vmoptions\\rubymine.vmoptions','PROCESSOR_REVISION': '9e0a','PUBLIC': 'C:\\Users\\<USER>\\pojie\\ja-netfilter-all\\vmoptions\\pycharm.vmoptions','PATH': 'D:\\dcode\\lin_trans\\.venv/Scripts;C:\\Program Files\\ShadowBot;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6\\libnvvp;C:\\Program Files (x86)\\VMware\\VMware Workstation\\bin\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;d:\\Program Files\\Git\\cmd;D:\\Program Files\\mingw64\\bin;D:\\Program Files\\CMake\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\nodejs\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.mujoco\\mjpro150\\bin;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2024.3.0\\;E:\\tool\\ffmpeg;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Scripts\\;C:\\Users\\<USER>\\.local\\bin;C:\\Program Files\\ShadowBot;C:\\Users\\<USER>\\scoop\\shims;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\Bandizip\\;C:\\Program Files\\JetBrains\\PyCharm 2023.1.1\\bin;C:\\WIND\\WIND.NET.CLIENT\\WWT\\bin;C:\\WIND\\WIND.NET.CLIENT\\WWT\\x64;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;C:\\Programs\\Microsoft VS Code\\bin;C:\\dev\\flutter\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Programs\\oh-my-posh\\bin;C:\\Users\\<USER>\\.mujoco\\mjpro150\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin','DRIVERDATA': 'C:\\Windows\\System32\\Drivers\\DriverData','HOMEDRIVE': 'C:','IDEA_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\idea.vmoptions','SESSIONNAME': 'Console','LOGONSERVER': '\\\\DESKTOP-PL0TGPJ','APPCODE_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\appcode.vmoptions','JETBRAINSCLIENT_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\jetbrainsclient.vmoptions','EFC_14348_1592913036': '1','HOMEPATH': '\\Users\\gaosh','SYSTEMROOT': 'C:\\WINDOWS','SHADOWBOT_ROOT_X64': 'C:\\Program Files\\ShadowBot','CHOCOLATEYINSTALL': 'C:\\ProgramData\\chocolatey','GLOG_LOGBUFSECS': '0','UV_CACHE_DIR': 'D:\\uv\\cache','LOCALAPPDATA': 'C:\\Users\\<USER>\\AppData\\Local','MUJOCO_PY_MJPRO_PATH': 'C:\\Users\\<USER>\\.mujoco\\mjpro150','APPDATA': 'C:\\Users\\<USER>\\AppData\\Roaming','PROCESSOR_IDENTIFIER': 'Intel64 Family 6 Model 158 Stepping 10, GenuineIntel','PATHEXT': '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL','PSMODULEPATH': 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules','PROGRAMFILES(X86)': 'C:\\Program Files (x86)','VIRTUAL_ENV': 'D:\\dcode\\lin_trans\\.venv','VIRTUAL_ENV_PROMPT': 'lin_trans','WEBIDE_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\webide.vmoptions','PHPSTORM_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\phpstorm.vmoptions','SDL_AUDIODRIVER': 'directsound','RIDER_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\rider.vmoptions','JETBRAINS_INTELLIJ_COMMAND_END_MARKER': 'zAG9cHrGhpp5D6ksCzh7Brd07V8MA5DbeaJc2yVZd8sHY25bUJMGBV8oWtHgmOnZ','SHADOWBOT_CULTURE': 'Zh_CN','NVTOOLSEXT_PATH': 'C:\\Program Files\\NVIDIA Corporation\\NvToolsExt\\','WEBSTORM_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\webstorm.vmoptions','PROCESSOR_ARCHITECTURE': 'AMD64','OS': 'Windows_NT','CUDA_PATH_V12_6': 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6','COMSPEC': 'C:\\WINDOWS\\system32\\cmd.exe','NUMBER_OF_PROCESSORS': '6','PROCESSOR_LEVEL': '6','WINDIR': 'C:\\WINDOWS','USERDOMAIN_ROAMINGPROFILE': 'DESKTOP-PL0TGPJ','PROGRAMFILES': 'C:\\Program Files','DATASPELL_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\dataspell.vmoptions','TEMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','POSH_THEMES_PATH': 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\oh-my-posh\\themes','TMP': 'C:\\Users\\<USER>\\AppData\\Local\\Temp','GATEWAY_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\gateway.vmoptions','ONEDRIVE': 'E:\\onedrive','ONEDRIVECONSUMER': 'E:\\onedrive','USERDOMAIN': 'DESKTOP-PL0TGPJ','SYSTEMDRIVE': 'C:','MUJOCO_PY_MJKEY_PATH': 'C:\\Users\\<USER>\\.mujoco\\mjpro150\\bin\\mjkey.txt','COMPUTERNAME': 'DESKTOP-PL0TGPJ','PROGRAMDATA': 'C:\\ProgramData','CUDA_PATH': 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.6','COMMONPROGRAMFILES(X86)': 'C:\\Program Files (x86)\\Common Files','GOLAND_VM_OPTIONS': 'E:\\pojie\\ja-netfilter-all\\vmoptions\\goland.vmoptions','CHOCOLATEYLASTPATHUPDATE': '133643600085253196','COMMONPROGRAMFILES': 'C:\\Program Files\\Common Files','_CONDA_EXE': 'C:\\ac\\Scripts\\conda.exe','_CONDA_ROOT': 'C:\\ac','NUITKA_CACHE_DIR_DOWNLOADS': 'C:\\Users\\<USER>\\AppData\\Local\\Nuitka\\Nuitka\\Cache\\DOWNLO~1','NUITKA_PYTHON_EXE_PATH': 'D:\\dcode\\lin_trans\\.venv\\Scripts\\python.exe','NUITKA_PACKAGE_DIR': 'D:\\dcode\\lin_trans\\.venv\\Lib\\site-packages\\nuitka','_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT': '5000','_NUITKA_ONEFILE_TEMP_SPEC': '{TEMP}\\onefile_{PID}_{TIME}','_NUITKA_ONEFILE_COMPRESSION_BOOL': '1','_NUITKA_ONEFILE_ARCHIVE_BOOL': '0','_NUITKA_BUILD_DEFINITIONS_CATALOG': '_NUITKA_ONEFILE_CHILD_GRACE_TIME_INT,_NUITKA_ONEFILE_TEMP_SPEC,_NUITKA_ONEFILE_COMPRESSION_BOOL,_NUITKA_ONEFILE_ARCHIVE_BOOL,_NUITKA_BUILD_DEFINITIONS_CATALOG','NUITKA_QUIET': '0'},
    shell=False
)