��       �request�.��       �
connection�.��       �
AsyncEvent�.��       �_connection_acquired�.��       �set�.��       �self�.��       �wait�.��       �timeout�.��       h��.��       �wait_for_connection�.��(       �$AsyncPoolRequest.wait_for_connection�.��       �_ssl_context�.��
       �_proxy�.��       ��������.��       �_max_connections�.��       �_max_keepalive_connections�.��       �min�.��       �_keepalive_expiry�.��
       �_http1�.��
       �_http2�.��       �_retries�.��       �_local_address�.��       �_uds�.��       �AutoBackend�.��       �_network_backend�.��       �_socket_options�.��       �_connections�.��
       �	_requests�.��       �AsyncThreadLock�.��       �_optional_thread_lock�.��"      X  
        A connection pool for making HTTP requests.

        Parameters:
            ssl_context: An SSL context to use for verifying connections.
                If not specified, the default `httpcore.default_ssl_context()`
                will be used.
            max_connections: The maximum number of concurrent HTTP connections that
                the pool should allow. Any attempt to send a request on a pool that
                would exceed this amount will block until a connection is available.
            max_keepalive_connections: The maximum number of idle HTTP connections
                that will be maintained in the pool.
            keepalive_expiry: The duration in seconds that an idle HTTP connection
                may be maintained for before being expired from the pool.
            http1: A boolean indicating if HTTP/1.1 requests should be supported
                by the connection pool. Defaults to True.
            http2: A boolean indicating if HTTP/2 requests should be supported by
                the connection pool. Defaults to False.
            retries: The maximum number of retries when trying to establish a
                connection.
            local_address: Local address to connect from. Can also be used to connect
                using a particular address family. Using `local_address="0.0.0.0"`
                will connect using an `AF_INET` address (IPv4), while using
                `local_address="::"` will connect using an `AF_INET6` address (IPv6).
            uds: Path to a Unix Domain Socket to use instead of TCP sockets.
            network_backend: A backend instance to use for handling network I/O.
            socket_options: Socket options that have to be included
             in the TCP socket when the connection was established.
        �.��       �url�.��
       �scheme�.��       Csocks5�Csocks5h���.��       �socks_proxy�.��       �AsyncSocks5Connection���.�h$.��
       �origin�.��       �auth�.��s       (�proxy_origin��
proxy_auth��
remote_origin��ssl_context��keepalive_expiry��http1��http2��network_backend�t�.��       Chttp�.��       �
http_proxy�.��        �AsyncForwardHTTPConnection���.�h3.��       �headers�.�h+.��0       (h(�
proxy_headers��proxy_ssl_context�h*h,h/t�.��       �AsyncTunnelHTTPConnection���.�h9.��       (h(h6h7h*h+h,h-h.h/t�.��       �AsyncHTTPConnection�.��A       (h&h+h,h-h.�retries��
local_address��uds�h/�socket_options�t�.���      X�  
        Return a list of the connections currently in the pool.

        For example:

        ```python
        >>> pool.connections
        [
            <AsyncHTTPConnection ['https://example.com:443', HTTP/1.1, ACTIVE, Request Count: 6]>,
            <AsyncHTTPConnection ['https://example.com:443', HTTP/1.1, IDLE, Request Count: 9]> ,
            <AsyncHTTPConnection ['http://example.com:80', HTTP/1.1, IDLE, Request Count: 1]>,
        ]
        ```
        �.���       ��
        Send an HTTP request, and return an HTTP response.

        This is the core implementation that is called into by `.request()` or `.stream()`.
        �.��
       �decode�.��       � �.��       �UnsupportedProtocol�.��A       �;Request URL is missing an 'http://' or 'https://' protocol.���.��       (�http��https��ws��wss�t�.��-       �)Request URL has an unsupported protocol '�.��	       �://'.�.��       �
extensions�.��       �get�.��       �pool�N��.��
       �	__enter__�.��       �__exit__�.��       �AsyncPoolRequest�.��
       �append�.��       NNN��.��#       �_assign_requests_to_connections�.��       �_close_connections�.��       �closing�.��       �pool_request�.��       �handle_async_request�.��       �ConnectionNotAvailable�.��       �clear_connection�.��
       �remove�.��       �response�.��
       �stream�.��       �
AsyncIterable�.��       �Response�.��
       �status�.��       �PoolByteStream�.��       hbh\�pool���.��       (heh5�content�hPt�.��,       �(AsyncConnectionPool.handle_async_request�.��
       �	is_closed�.��       �has_expired�.��       �closing_connections�.��       �is_idle�.��
       �	is_queued�.��       �can_handle_request�.��       �is_available�.��       �assign_to_connection�.��       �create_connection�.��L      XE  
        Manage the state of the connection pool, assigning incoming
        requests to connections as available.

        Called whenever a new request is added or removed from the pool.

        Any closing connections are returned, allowing the I/O for closing
        those connections to be handled seperately.
        �.��       �AsyncShieldCancellation�.��
       �aclose�.��*       �&AsyncConnectionPool._close_connections�.��       �AsyncConnectionPool.aclose�.��       �
__aenter__�.��"       �AsyncConnectionPool.__aenter__�.��
       �	__aexit__�.��!       �AsyncConnectionPool.__aexit__�.��       �__name__�.��	       �count�.��       ���.��       ���.��       �
Requests: �.��       �num_active_requests�.��
       �	 active, �.��       �num_queued_requests�.��       � queued�.��       �
Connections: �.��       �num_active_connections�.��       �num_idle_connections�.��	       � idle�.��       �<�.��       � [�.��       � | �.��       �]>�.��       �_stream�.��       �
_pool_request�.��	       �_pool�.��       �_closed�.��
       �	__aiter__�.��       �PoolByteStream.__aiter__�.��       �PoolByteStream.aclose�.��       �__doc__�.��       �__file__�.��       �has_location�.��       �
__cached__�.��       �annotations�.��       �ssl�.��       �sys�.��	       �types�.��
       �typing�.��       �_backends.auto�.��       h��.�K.��       �_backends.base�.��)       �
SOCKET_OPTION��AsyncNetworkBackend���.�h�.�h�.��       �_exceptions�.��       h^hF��.��       �_models�.��!       (�Origin��Proxy��Request�hdt�.�h�.�h�.�h�.��       �_synchronization�.��	       hhvh��.��       h<��.��       �
interfaces�.��6       �AsyncConnectionInterface��AsyncRequestInterface���.�h�.�h�.��#       �httpcore._async.connection_pool�.��       �
__module__�.��       �__qualname__�.��       }�(h h��return��None�u.��       �__init__�.��       �AsyncPoolRequest.__init__�.��-       }�(h�AsyncConnectionInterface | None�h�h�u.��)       �%AsyncPoolRequest.assign_to_connection�.��       }�h�h�s.��%       �!AsyncPoolRequest.clear_connection�.��       N��.��       }�(h�float | None�h�h�u.��
       }�h��bool�s.��       �AsyncPoolRequest.is_queued�.��       �__prepare__�.��       �AsyncConnectionPool�.��       �__getitem__�.��2       �.%s.__prepare__() must return a mapping, not %s�.��       �<metaclass>�.��9       �5
    A connection pool for making HTTP requests.
    �.��       (NNK
NN��K NNNNt�.��
      }�(h+�ssl.SSLContext | None��proxy��Proxy | None��max_connections��
int | None��max_keepalive_connections��
int | None�h,�float | None�h-h�h.h�h=�int�h>�
str | None�h?�
str | None�h/�AsyncNetworkBackend | None�h@�%typing.Iterable[SOCKET_OPTION] | None�h�h�u.��        �AsyncConnectionPool.__init__�.��
       }�(h&h�h�h�u.��)       �%AsyncConnectionPool.create_connection�.��       �property�.��'       }�h��list[AsyncConnectionInterface]�s.��       �connections�.��#       �AsyncConnectionPool.connections�.��
       }�(h h�h�hdu.��7       �3AsyncConnectionPool._assign_requests_to_connections�.��,       }�(h[�list[AsyncConnectionInterface]�h�h�u.��       }�h�h�s.��}       }�(�exc_type��type[BaseException] | None��	exc_value��BaseException | None��	traceback��types.TracebackType | None�h�h�u.��       }�h��str�s.��       �__repr__�.��        �AsyncConnectionPool.__repr__�.��       �__orig_bases__�.��1       }�(hb�typing.AsyncIterable[bytes]�h\hVhgh�h�h�u.��       �PoolByteStream.__init__�.��$       }�h��typing.AsyncIterator[bytes]�s.��&       �"httpcore\_async\connection_pool.py�.��,       �(<module httpcore._async.connection_pool>�.��       �	__class__���.��       h��.��       (hh�h�h�t�.��       h�part��exc���.��       (hh+h�h�h�h,h-h.h=h>h?h/h@t�.��       hh ��.��       (hhbh\hgt�.��f       (h�
class_name��request_is_queued��connection_is_idle�h�h�h�h��
requests_info��connection_info�t�.��K       (hhnh�queued_requests�h\h&�available_connections��idle_connections�t�.��	       hh[h��.��       hhn��.��       hh[��.��       hh��.��       (hh&h$h3h9t�.��$       (hh h�timeouts�hh\h[hhaj   t�.��       hh��.��       �__spec__�.