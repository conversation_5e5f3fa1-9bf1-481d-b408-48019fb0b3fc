��'       �#get_address_port_tuple_from_address�.��       �encode_address�.��       �AddressType�.��       �IPV4�.��       �
SOCKSError�.��A       �;IPv6 addresses and domain names are not supported by SOCKS4���.��&       (�command��addr��port��user_id�t�.���      X�  Convenience class method to build an instance from command and address.

        Args:
            command: The command to request.
            address: A string in the form 'HOST:PORT' or a tuple of ip address string
                and port number.
            user_id: Optional user ID.

        Returns:
            A SOCKS4Request instance.

        Raises:
            SOCKSError: If a domain name or IPv6 address was supplied.
        �.�h
.��3       �-SOCKS4 requires a user_id, none was specified���.��       C �.��       C�.�h.�h	.��       �to_bytes�.��       K�big���.��       �	byteorder���.�h.��       C �.���      X�  Packs the instance into a raw binary in the appropriate form.

        Args:
            user_id: Optional user ID as an override, if not provided the instance's
                will be used, if none was provided at initialization an error is raised.

        Returns:
            The packed request.

        Raises:
            SOCKSError: If no user was specified in this call or on initialization.
        �.��j      Xc  Convenience class method to build an instance from command and address.

        Args:
            command: The command to request.
            address: A string in the form 'HOST:PORT' or a tuple of ip address string
                and port number.
            user_id: Optional user ID.

        Returns:
            A SOCKS4ARequest instance.
        �.��       C   ��.��       �builtins��slice���K KN��R�.��       �
ProtocolError�.��       �Malformed reply���.��       �SOCKS4ReplyCode�.��       hKKN��R�.��       �
from_bytes�.��       hKKN��R�.��       }�h�big�s.��       �decode_address�.��       hKKN��R�.��       �
reply_code�h	h��.���       ��Unpacks the reply data into an instance.

        Returns:
            The unpacked reply instance.

        Raises:
            ProtocolError: If the data does not match the spec.
        �.��       �        �.��       �
_data_to_send�.��       �_received_data�.��	       �dumps�.��       h
��.���       ��Packs a request object and adds it to the send data buffer.

        Args:
            request: The request instance to be packed.
        �.��       �SOCKS4Reply�.��	       �loads�.���       ��Unpacks response data into a reply object.

        Args:
            data: The raw response data from the proxy server.

        Returns:
            The appropriate reply object.
        �.��u       �qReturns the data to be sent via the I/O library of choice.

        Also clears the connection's buffer.
        �.��       �__doc__�.��       �__file__�.��
       �origin�.��       �has_location�.��       �
__cached__�.��       �enum�.��
       �typing�.��
       �_types�.��       �
StrOrBytes���.�hB.��       �
exceptions�.��       hh��.��	       �utils�.��       (hh*hh t�.��       �Enum�.��       �__prepare__�.��       �__getitem__�.��2       �.%s.__prepare__() must return a mapping, not %s�.��       �__name__�.��       �<metaclass>�.��       �socksio.socks4�.��       �
__module__�.��&       �"Enumeration of SOCKS4 reply codes.�.��       �__qualname__�.��       CZ�.��       �REQUEST_GRANTED�.��       C[�.��       �REQUEST_REJECTED_OR_FAILED�.��       C\�.��       �CONNECTION_FAILED�.��       C]�.��       �AUTHENTICATION_FAILED�.��       �__orig_bases__�.��       �
SOCKS4Command�.��(       �$Enumeration of SOCKS4 command codes.�.��       C�.��       �CONNECT�.��       C�.��       �BIND�.��       �
NamedTuple�.��       �
SOCKS4Request�.��q      Xj  Encapsulates a request to the SOCKS4 proxy server

    Args:
        command: The command to request.
        port: The port number to connect to on the target host.
        addr: IP address of the target host.
        user_id: Optional user ID to be included in the request, if not supplied
            the user *must* provide one in the packing operation.
    �.��       �__annotations__�.��       �int�.��	       �bytes�.��       �Optional�.��       �classmethod�.��       N��.��       �address�.��	       �Union�.��	       �Tuple�.��
       �return�.��       �from_address�.��       �SOCKS4Request.from_address�.��       �SOCKS4Request.dumps�.��       �SOCKS4ARequest�.��r      Xk  Encapsulates a request to the SOCKS4A proxy server

    Args:
        command: The command to request.
        port: The port number to connect to on the target host.
        addr: IP address of the target host.
        user_id: Optional user ID to be included in the request, if not supplied
            the user *must* provide one in the packing operation.
    �.��       �SOCKS4ARequest.from_address�.��       �SOCKS4ARequest.dumps�.���       ��Encapsulates a reply from the SOCKS4 proxy server

    Args:
        reply_code: The code representing the type of reply.
        port: The port number returned.
        addr: Optional IP address returned.
    �.�h-.��       �str�.��       �data�.��       �SOCKS4Reply.loads�.���       ��Encapsulates a SOCKS4 and SOCKS4A connection.

    Packs request objects into data suitable to be send and unpacks reply
    data into their appropriate reply objects.

    Args:
        user_id: The user ID to be sent as part of the requests.
    �.��       �SOCKS4Connection�.��       }�h
h�bytes���s.��       �__init__�.��       �SOCKS4Connection.__init__�.��       �request�.��       �send�.��       �SOCKS4Connection.send�.��       �receive_data�.��!       �SOCKS4Connection.receive_data�.��       }�hmh}s.��       �data_to_send�.��!       �SOCKS4Connection.data_to_send�.��       �socksio\socks4.py�.��       �<module socksio.socks4>�.��       �	__class__���.��       �self�h
��.��       h�hw��.��)       (�cls�hhjh
h	�atype��encoded_addr�t�.��
       h�hw�exc���.��	       h�h�h
��.��       � �.��       �__spec__�.