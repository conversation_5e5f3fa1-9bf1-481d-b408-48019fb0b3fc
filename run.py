import sys

from PySide6.QtWidgets import QApplication

from nice_ui.ui.MainWindow import Window


def main():
    # 添加调试信息（仅在调试模式下显示）
    print("🚀 程序启动中...")

    # 测试关键模块导入
    try:
        import httpx
        print(f"✅ httpx 导入成功，版本: {httpx.__version__}")
    except ImportError as e:
        print(f"❌ httpx 导入失败: {e}")
        print("这可能是打包配置问题")
        # 不要退出，让程序继续运行，看看是否其他功能正常

    app = QApplication(sys.argv)
    window = Window()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
